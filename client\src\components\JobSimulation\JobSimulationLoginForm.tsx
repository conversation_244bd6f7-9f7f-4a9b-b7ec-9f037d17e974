import { format } from 'date-fns';
import { motion } from 'framer-motion';
import type * as t from 'librechat-data-provider';
import { EyeIcon, EyeOffIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useSetRecoilState } from 'recoil';
import { TJobSimulationContext, TResError } from '~/common';
import { useLoginUserJobSimulationMutation } from '~/data-provider';
import {
  useDeleteUserInteraction,
  useSaveUserInteraction,
} from '~/data-provider/JobSimulation/mutations';
import { useAuthContext } from '~/hooks';
import store from '~/store';
import { Lock2Icon, User2Icon } from '../svg';
import JobSimulationLogo from './JobSimulationLogo';

const FormFieldError = ({ error }: { error?: string }) => {
  return error ? <p className="mt-1 px-2 text-sm text-red-500">{error}</p> : <></>;
};

const JobSimulationLoginForm = () => {
  const { isAuthenticated } = useAuthContext();
  const { jobSimulationId, jobSimulationData } = useOutletContext<TJobSimulationContext>();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState<{ username?: string; password?: string }>({});
  const [showPassword, setShowPassword] = useState(false);
  const [credentials, setCredentials] = useState<{
    username: string;
    password: string;
    email: string;
  } | null>(null);

  const setJobSimulationUser = useSetRecoilState(store.jobSimulationUser);
  const setJobsimulationTriggerMessage = useSetRecoilState(store.jobSimulationTriggerMessage);

  const formattedDate = format(new Date(), 'EEEE, d MMMM');
  const formattedTime = format(new Date(), 'HH:mm');

  const loginUserJobSimulation = useLoginUserJobSimulationMutation({
    onSuccess: (data: { user: t.TUser }) => {
      const { user } = data;
      setJobSimulationUser({
        name: user.name,
        email: user.email,
        username: user.username,
        avatar: user.avatar,
      });

      let message = 'I have logged in to the Work Portal';
      if (
        jobSimulationData?.progress?.conversationId &&
        (jobSimulationData?.progress?.emails?.length || 0) > 1
      ) {
        message = 'I have logged in to the Work Portal to resume the Job Simulation';
      }
      setJobsimulationTriggerMessage({
        message,
        isTriggered: true,
      });

      saveUserInteraction.mutate({
        jobSimulationId: jobSimulationId!,
        jobSimulationEmail: user.email,
        interaction: {
          type: 'login',
        },
      });
    },
    onError: (error: TResError | unknown) => {
      const resError = error as TResError;
      console.log('error', resError?.response?.data?.message || resError?.message);
    },
  });

  const saveUserInteraction = useSaveUserInteraction();
  const deleteUserInteraction = useDeleteUserInteraction();

  const validateForm = () => {
    const newErrors: { username?: string; password?: string } = {};

    if (!credentials || !credentials.username || !credentials.password) {
      newErrors.username = 'Invalid job simulation username';
    } else if (!username) {
      newErrors.username = 'Username is required';
    } else if (username !== credentials.username) {
      newErrors.username = 'Invalid username format';
    }

    // Validate password
    else if (!password) {
      newErrors.password = 'Password is required';
    } else if (password !== credentials.password) {
      newErrors.password = 'Invalid password';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      // Log form data

      // Call loginJobSimulation function from context
      loginUserJobSimulation.mutate({
        jobSimulationEmail: credentials?.email,
        username,
        password,
        jobSimulationId: jobSimulationId!,
      });
    }
  };

  let logoSrc;
  switch (jobSimulationId) {
    case 'digital-marketing':
      logoSrc = '/assets/job-simulation/bg-logo.png';
      break;
    case 'esg-analyst':
      // logoSrc = '/assets/job-simulation/background-greentek-hive.png';
      logoSrc = '/assets/job-simulation/bg-logo.png';
      break;
    default:
      logoSrc = '/assets/job-simulation/bg-logo.png';
  }

  useEffect(() => {
    if (!jobSimulationId || !isAuthenticated || !jobSimulationData?.credentials) return;
    const jsCredentials = jobSimulationData.credentials;
    const randomEmail = jsCredentials.username
      ? `${jsCredentials.username}.${Math.random().toString(36).substring(2, 15)}@mailinator.com`
      : '';

    setCredentials({
      username: jsCredentials.username,
      password: jsCredentials.password,
      email: randomEmail,
    });
  }, [jobSimulationId, isAuthenticated, jobSimulationData]);

  return (
    <div className="relative z-20 mx-auto flex h-full w-full max-w-[661px] flex-col items-center rounded-lg p-8 text-[#333]">
      {/* Image */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: 'easeOut' }}
        className="relative w-full"
      >
        {/* <div
          className="relative flex h-[200px] items-center justify-center"
          style={{
            backgroundImage: `url(${logoSrc})`,
            backgroundSize: 'contain',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
          }}
        >
          {jobSimulationData?.logo && (
            <img src={jobSimulationData.logo} alt="Logo" className="z-[1] max-w-[36%]" />
          )}
        </div> */}
        <div>
          <JobSimulationLogo logo={jobSimulationData?.logo || ''} />
        </div>
        <div>
          <p className="text-center text-lg">{formattedDate}</p>
          <p className="text-center text-5xl font-semibold">{formattedTime}</p>
        </div>
      </motion.div>
      {/* Login Form */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: 'easeOut', delay: 0.2 }}
        className="mt-[80px] w-full max-w-sm"
      >
        <h2 className="mb-6 text-center text-4xl font-semibold">Welcome back</h2>
        <p className="mb-2.5 text-center text-base font-medium opacity-50">
          Login with your account
        </p>
        <form onSubmit={handleSubmit} className="space-y-2.5">
          {/* Email */}
          <div>
            <div className="flex items-center gap-5 rounded-full bg-white py-1 pl-1 pr-2.5 shadow-sm">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-[#E6E6E6]">
                <User2Icon className="mr-3 h-5 w-5 text-gray-400" />
              </div>
              <input
                id="username"
                type="text"
                placeholder="username"
                onChange={(e) => setUsername(e.target.value)}
                className="w-full flex-1 bg-transparent text-gray-800 outline-none placeholder:text-gray-400 focus:outline-none focus:ring-0"
              />
            </div>
            <FormFieldError error={errors.username} />
          </div>
          {/* Password */}
          <div>
            <div className="flex items-center gap-5 rounded-full bg-white py-1 pl-1 pr-2.5 shadow-sm">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-[#E6E6E6]">
                <Lock2Icon className="mr-3 h-5 w-5 text-gray-400" />
              </div>
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="********"
                className="w-full flex-1 bg-transparent text-gray-800 outline-none placeholder:text-gray-400 focus:outline-none focus:ring-0"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="text-gray-400"
              >
                {showPassword ? (
                  <EyeOffIcon className="h-5 w-5" />
                ) : (
                  <EyeIcon className="h-5 w-5" />
                )}
              </button>
            </div>
            <FormFieldError error={errors.password} />
          </div>
          {/* <p className='text-xs font-medium text-center opacity-50 cursor-pointer hover:underline'>Forgot password?</p> */}
          <div className="pt-2">
            <button
              type="submit"
              disabled={loginUserJobSimulation.isLoading}
              className="flex w-full justify-center rounded-full border border-white/20 bg-white/10 px-6 py-3 text-lg font-medium shadow-[0_8px_30px_rgb(0,0,0,0.12)] backdrop-blur-xl transition-all duration-300 hover:bg-white/20 hover:shadow-[0_12px_40px_rgba(0,0,0,0.2)]"
            >
              {loginUserJobSimulation.isLoading ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-loader-icon lucide-loader animate-spin"
                >
                  <path d="M12 2v4" />
                  <path d="m16.2 7.8 2.9-2.9" />
                  <path d="M18 12h4" />
                  <path d="m16.2 16.2 2.9 2.9" />
                  <path d="M12 18v4" />
                  <path d="m4.9 19.1 2.9-2.9" />
                  <path d="M2 12h4" />
                  <path d="m4.9 4.9 2.9 2.9" />
                </svg>
              ) : (
                'Login'
              )}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};

export default JobSimulationLoginForm;
